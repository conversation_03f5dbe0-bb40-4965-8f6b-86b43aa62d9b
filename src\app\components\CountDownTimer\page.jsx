"use client";
import { useState, useEffect } from "react";
import styles from "./countDownTimer.module.css";

const CountDownTimer = ({ duration, tradeStatus, className }) => {
  const [time, setTime] = useState(duration || 0);
  const [isActive, setIsActive] = useState(false);



  const getFormattedTime = (milliseconds) => {
    if (milliseconds <= 0) return "00:00:00";

    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  };

  // Check if timer should be active based on trade status
  useEffect(() => {
    const activeStatuses = ["active", "pending", "in_progress", "waiting", "ongoing"];
    const inactiveStatuses = ["completed", "expired", "rejected"];

    // If no status provided, assume active (default behavior)
    if (!tradeStatus) {
      setIsActive(true);
      return;
    }

    // Check if status is explicitly inactive
    if (inactiveStatuses.includes(tradeStatus.toLowerCase())) {
      setIsActive(false);
      return;
    }

    // For any other status or active statuses, show the timer
    setIsActive(true);
  }, [tradeStatus]);

  // Timer countdown effect
  useEffect(() => {
    if (!duration || !isActive) return;

    setTime(duration);
    const timer = setInterval(() => {
      setTime((prev) => {
        const newTime = prev - 1000;

        if (newTime <= 0) {
          clearInterval(timer);
          return 0;
        }

        return newTime;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [duration, isActive]);

  // Don't render if trade is not active
  if (!isActive) {
    return null;
  }

  return (
    <div className={`${styles.countDownTimer} ${className || ""}`}>
      {getFormattedTime(time)}
    </div>
  );
};

export default CountDownTimer;
