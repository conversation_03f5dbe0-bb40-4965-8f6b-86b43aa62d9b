"use client";
import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import styles from "./countDownTimer.module.css";

const CountDownTimer = ({ duration, tradeStatus, className, orderNumber }) => {
  const [time, setTime] = useState(duration || 0);
  const [isActive, setIsActive] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [timerIncreased, setTimerIncreased] = useState(false);

  const handleTimeIncrease = async () => {
    const payload = {
      add_minutes: 4,
      order_id: orderNumber,
    };
    const res = await customFetchWithToken.post("trade/increase-time/", payload);
    if (res.status === 200) {
      toast.success("Trade time increased successfully");
    } else {
      toast.error("Failed to increase trade time");
    }
    setShowConfirmation(false);
    setTimerIncreased(true);
  };

  const handleTimeReject = () => {
    setShowConfirmation(false);
  };

  const getFormattedTime = (milliseconds) => {
    if (milliseconds <= 0) return "00:00:00";

    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  };

  // Check if timer should be active based on trade status
  useEffect(() => {
    const activeStatuses = ["active", "pending", "in_progress", "waiting", "ongoing"];
    const inactiveStatuses = ["completed", "expired", "rejected"];

    // If no status provided, assume active (default behavior)
    if (!tradeStatus) {
      setIsActive(true);
      return;
    }

    // Check if status is explicitly inactive
    if (inactiveStatuses.includes(tradeStatus.toLowerCase())) {
      setIsActive(false);
      return;
    }

    // For any other status or active statuses, show the timer
    setIsActive(true);
  }, [tradeStatus]);

  // Timer countdown effect
  useEffect(() => {
    if (!duration || !isActive) return;

    setTime(duration);
    const timer = setInterval(() => {
      setTime((prev) => {
        const newTime = prev - 1000;

        // Check for timer increase prompt at 9 minutes remaining (540000ms = 9 minutes)
        if (newTime === 540000 && !timerIncreased) {
          setShowConfirmation(true);
        }

        if (newTime <= 0) {
          clearInterval(timer);
          return 0;
        }

        return newTime;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [duration, isActive, timerIncreased]);

  // Don't render if trade is not active
  if (!isActive) {
    return null;
  }

  return (
    <>
      <div className={`${styles.countDownTimer} ${className || ""}`}>
        {getFormattedTime(time)}
      </div>

      {showConfirmation && (
        <div className={styles.alertPopup}>
          <p>
            Your order {orderNumber}: Do you want to increase trade time by 4
            minutes?
          </p>
          <div className={styles.btnContainer}>
            <button className={styles.yesBtn} onClick={handleTimeIncrease}>
              Yes
            </button>
            <button className={styles.noBtn} onClick={handleTimeReject}>
              No
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default CountDownTimer;
