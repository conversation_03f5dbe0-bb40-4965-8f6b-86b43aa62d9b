@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap");

.main {
    background: linear-gradient(45deg, #f8f9fb 0%, #f8f9fb00 100%);
    min-height: 100vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    font-family: poppins;
    /* position: relative; */
}

/* Mobile Navigation Bar - Inspired by SidebarHeader */
.hamMenu {
    display: none;

    @media screen and (max-width: 576px) {
        display: flex;
        width: 100%;
        background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #1e1b4b 100%);
        position: fixed;
        top: 0;
        height: 56px;
        z-index: 1200;
        justify-content: space-between;
        align-items: center;
        padding: 0 16px;
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 
            0 2px 8px rgba(0, 0, 0, 0.1),
            0 1px 4px rgba(0, 0, 0, 0.04);
        transition: all 0.3s ease;
    }
}



/* Enhanced Mobile Menu Overlay */
.menuOverlay {
    display: none;
    
    @media screen and (max-width: 576px) {
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(8px);
        z-index: 800;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
}

.menuOverlayVisible {
    composes: menuOverlay;
    opacity: 1;
    pointer-events: auto;
}

/* Left Menu Button - SidebarHeader Style */
.hamMenuLeft {
    display: none;

    @media screen and (max-width: 576px) {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        padding: 8px;
        border-radius: 12px;
        width: 36px;
        height: 36px;
        position: relative;
        z-index: 2;
    }
}



.hamMenuLeft:hover {
    @media screen and (max-width: 576px) {
        background: rgba(255, 255, 255, 0.12);
        border-color: rgba(255, 255, 255, 0.18);
        transform: translateY(-1px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
}

.hamMenuLeft:active {
    @media screen and (max-width: 576px) {
        transform: translateY(0);
        background: rgba(255, 255, 255, 0.1);
        transition: all 0.1s ease;
    }
}

.hamMenuCenter {
    display: none;

    @media screen and (max-width: 576px) {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        position: relative;
        z-index: 2;
    }
}

.hamMenuCenter > div {
    @media screen and (max-width: 576px) {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
    }
}

.hamMenuCenter img {
    @media screen and (max-width: 576px) {
        display: none;
    }
}

.brandText {
    @media screen and (max-width: 576px) {
        font-family: 'Poppins', sans-serif;
        font-size: 18px;
        font-weight: 700;
        color: #ffffff;
        margin: 0;
        text-align: center;
        letter-spacing: -0.02em;
        background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }
}

.hamMenuRight {
    display: none;

    @media screen and (max-width: 576px) {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        z-index: 2;
        height: 100%;
    }
}

.hamMenuRight :global(.rightContainerBell) {
    @media screen and (max-width: 576px) {
        margin: 0;
        padding: 0;
        display: flex;
        align-items: center;
        height: 100%;
    }
}

.hamMenuRight :global(.icon_button) {
    @media screen and (max-width: 576px) {
        width: 36px;
        height: 36px;
        min-height: 36px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        padding: 0;
        margin: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.hamMenuRight :global(.material_icons) {
    @media screen and (max-width: 576px) {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }
}

.hamMenuRight :global(.icon_button__badge) {
    @media screen and (max-width: 576px) {
        width: 18px;
        height: 18px;
        top: -5px;
        right: -5px;
        font-size: 10px;
    }
}

.hamMenuRight :global(.notificationLists) {
    @media screen and (max-width: 576px) {
        top: 50px;
        right: 0;
        width: 300px;
    }
}

.hamMenuRight:hover {
    @media screen and (max-width: 576px) {
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    }
}

.hamMenuRight:active {
    @media screen and (max-width: 576px) {
        transform: scale(1);
        transition: all 0.1s ease;
    }
}

.leftContainerWrapper {
    width: 20%;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1100;
    padding: 30px 10px;

    @media screen and (max-width: 576px) {
        width: 85% !important;
        max-width: 320px;
        position: fixed;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
        backdrop-filter: blur(20px);
        height: 100vh;
        padding: 56px 0 0 0;
        transform: translateX(0);
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 
            8px 0 32px rgba(0, 0, 0, 0.12),
            4px 0 16px rgba(0, 0, 0, 0.08);
        top: 0;
        left: 0;
        border-right: 1px solid rgba(99, 102, 241, 0.1);
    }
}

.leftContainerWrapperHide {
    composes: leftContainerWrapper;

    @media screen and (max-width: 576px) {
        transform: translateX(-100%);
    }
}

.leftContainer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 10px;

    @media screen and (max-width: 576px) {
        width: 100%;
        height: 100%;
        padding: 0;
        overflow-y: auto;
        border-radius: 0;
        /* Enhanced scrollbar */
        scrollbar-width: thin;
        scrollbar-color: rgba(99, 102, 241, 0.3) transparent;
    }
}

/* Custom scrollbar for webkit browsers - Matching SidebarHeader */
.leftContainer::-webkit-scrollbar {
    width: 6px;
}

.leftContainer::-webkit-scrollbar-track {
    background: transparent;
}

.leftContainer::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #3b82f6, #8b5cf6);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.leftContainer::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #2563eb, #7c3aed);
}

/* hamMenu */

.wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
    margin: auto;
    justify-content: space-between;

    @media screen and (max-width : 576px) {
        /* display: none; */
        width: 100%;
        height: 100vh;
    }
}

.sidebar {
    width: 100%;
    height: auto;
    border-radius: 15px;
    background: #fff;


}

.pages {
    margin-top: 30px;

    width: 100%;
    height: 100%;
}

.dashboardContainer {
    width: 100%;
    height: 47px;
    /* background-color: aqua; */
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #4153ed;
    border-bottom: 1px solid #ececec;
}

.historyContainer {
    width: 100%;
    height: 47px;
    /* background-color: aqua; */
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #4153ed;
}

.logoutContainer {
    /* margin-bottom: auto; */
    width: 100%;
    height: 47px;
    /* background-color: aqua; */
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #4153ed;
    /* border-bottom: 1px solid #ECECEC; */
}

.sideIcons {
    margin-left: 20px;
    margin-right: 10px;
    color: #4153ed;
}

.dashboard {
    color: #4153ed;
}

.rightContainer {
    width: 75%;
    height: 100vh;
    display: flex;
    margin-left: 22%;
    overflow-y: auto;
    overflow-x: hidden;
    scroll-behavior: smooth;

    @media screen and (max-width : 576px) {
        width: 100%;
        margin-left: 0;
    }

}

/* Hide scrollbar for webkit browsers */
.rightContainer::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for Firefox */
.rightContainer {
    scrollbar-width: none;
}

.rightContainerWrapper {
    width: 100%;
    color: #000;
    font-family: Poppins;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    padding: 30px 10px;
    min-height: 100vh;
    @media screen and (max-width : 576px) {
        padding: 30px 0px;
    }
}

.rightContainerHeader {
    /* width: 100%; */
    height: 94px;
    color: #000;
    font-family: Poppins;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;

    @media screen and (max-width : 576px) {
        display: none;
    }
}

.rightContainerBody {
    min-height: calc(100vh - 94px);
    padding: 30px;
    border-radius: 20px;
    background: #fff;
    margin-bottom: 20px;

    @media screen and (max-width : 576px) {
        padding: 0px;
        min-height: calc(100vh - 140px);
        margin-top: 16px;
    }
}

.body {
    height: 100%;
    background-color: #fff;
    width: 100%;
}

.firstformWrapper {
    width: 100%;
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;

    @media screen and (max-width: 576px) {
        flex-direction: column;
    }
}

.secondformWrapper {
    width: 100%;
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    margin-top: 30px;

    @media screen and (max-width: 576px) {
        flex-direction: column;
    }
}

.thirdformWrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    margin-top: 30px;

    @media screen and (max-width: 576px) {
        flex-direction: column;
    }
}

.firstName {
    width: 46%;
    height: 40px;

    @media screen and (max-width: 576px) {
        width: 100%;
        margin: 40px 0px;
    }
}

.firstNameLabel {
    font-size: 12px;

    @media screen and (max-width: 576px) {
        margin-bottom: 5px;
        margin-left: 17px;
    }
}

.sec_firstName {
    width: 20%;
    height: 40px;

    @media screen and (max-width: 576px) {
        width: 100%;
        margin-bottom: 50px;
    }
}

.firstNameLabel {
    font-size: 12px;

    @media screen and (max-width: 576px) {
        margin-bottom: 5px;
    }
}

.firstNameInput {
    display: flex;

    @media screen and (max-width: 576px) {
        padding: 15px;
    }
}

.firstNameInput input {
    border: none;
    background-color: #f9f9f9;
    width: 100%;
    padding-left: 10px;
    padding-right: 10px;
    height: 40px;

    @media screen and (max-width: 576px) {
        margin-bottom: 5px;
        padding: none;
    }
}

.firstNameInput select {
    border: none;
    background-color: #f9f9f9;
    width: 100%;
    padding-left: 10px;
    height: 40px;
    padding-right: 10px;
}

.emailBtn {
    border-radius: 0px 2px 2px 0px;
    background: #f5f5f5;
    border: 1px solid #ebebeb;
    border-left: 2px solid #c4c3c3;
    font-size: 10px;
    font-weight: 600;
    width: 57px;
}

.addressName {
    width: 100%;
    margin-left: 23px;
}

.addressNameInput input {
    border: none;
    background-color: #f9f9f9;
    width: 96%;

    height: 40px;
    padding-left: 10px;
    padding-right: 10px;

    @media screen and (max-width: 576px) {
        width: 87%;

    }
}

.fourthformWrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 55px;
}

.paymentBoxContainer {
    width: 60%;
    height: auto;
    border-radius: 15px;
    border: 1px solid #d9d9d9;
    background: #fff;
    margin: auto;

}

.paymentBox {
    background-color: #4f535a;
    width: 100%;
    /* padding: 25px 20px; */

}

.paymentHeader {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    padding: 0px 15px;
    color: #000;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
}

.paymentDirection {
    padding: 0px 15px;
    /* background-color: #4153ed; */
    color: #000;
    text-align: center;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    display: flex;
    align-items: center;
    justify-content: center;
}

.payFrom {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    width: 50%;
    border-bottom: 1px solid black;
}

.payTo {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    width: 50%;
    border-bottom: 1px solid #efefef;
}

.fifthformWrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow-x: auto;
    margin-bottom: 20px;
    /* margin-top: 55px; */
}

.paymentGateways {
    width: 100px;
    height: 100px;
    background-color: #F9F9F9;
    margin: 20px 10px 10px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.BitcoinpaymentGateways {
    width: 100px;
    height: 100px;
    background-color: #F9F9F9;
    margin: 20px 10px 10px 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.listing_BtnCont {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
}

.listing_Btn {
    width: 365px;
    height: 50px;
    border-radius: 5px;
    border: 2px solid #4153ED;
    background: #FFF;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #4153ED;
    font-family: Poppins;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    cursor: pointer;

    @media screen and (max-width: 576px) {
        margin-bottom: 30px;

    }
}

.rightContainerBox {
    display: flex;
    justify-content: space-between;

}

.rightContainerBell {
    margin-right: 50px;
    display: flex;
    justify-content: center;
    position: relative;
    transition: 1s linear;
}


.icon_button {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    color: #333333;
    background: #dddddd;
    border: none;
    outline: none;
    border-radius: 50%;
    cursor: pointer;
  
}

.icon-button:hover {
    cursor: pointer;
}

.icon-button:active {
    background: #cccccc;
}

.icon_button__badge {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 25px;
    height: 25px;
    background: red;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
}

.notificationLists {
    position: absolute;
    height: 300px;
    width: 300px;
    background-color: #4153ED;
    top: 40px;
    right: 0px;
    border-radius: 12px;
    z-index: 1000;
    transition: height 1s linear, opacity 1s linear;
    opacity: 1;
    overflow: hidden;

    @media screen and (max-width: 576px) {
        z-index: 99999;
    }
}

.overlayMsg {
    position: fixed;
    right: 10px;
    bottom: 30px;
    width: 300px;
    height: 100px;
    background-color: #1448d6fd;
    border-radius: 5px;
    color: #f3eded;

    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    padding: 30px 20px;
}

.cancelTimerBtn {
    margin-top: 10px;
    border: none;
    padding: 5px 10px;
    font-family: poppins;
    font-weight: 700;
    border-radius: 5px;
    width: 100px;
    background-color: #f3eded;
    cursor: pointer;
}

/* Desktop Title Wrapper */
.desktopTitleWrapper {
    @media screen and (max-width: 576px) {
        display: none;
    }
}

/* Desktop Notification Wrapper */
.notificationWrapper {
    @media screen and (max-width: 576px) {
        display: none;
    }
}

/* Mobile Notification Section */
.mobileNotificationSection {
    display: none;
}

/* Profile Image Styling - Matching SidebarHeader */
.profileImage {
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    object-fit: cover;
    width: 100%;
    height: 100%;
}

.profileImage:hover {
    border-color: rgba(255, 255, 255, 0.3);
}

/* Hamburger Menu Icon - Enhanced */
.hamMenuLeft svg {
    width: 20px;
    height: 20px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.hamMenuLeft svg line {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
}

/* Menu Content Animation - Staggered like SidebarHeader */
@media screen and (max-width: 576px) {
    .leftContainer {
        opacity: 0;
        transform: translateX(-20px);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        transition-delay: 0.1s;
    }

    .leftContainerWrapper .leftContainer {
        opacity: 1;
        transform: translateX(0);
    }

    /* Staggered animation for menu items */
    .leftContainer > * {
        opacity: 0;
        transform: translateX(-20px);
        animation: slideInMenuItem 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }

    .leftContainer > *:nth-child(1) { animation-delay: 0.1s; }
    .leftContainer > *:nth-child(2) { animation-delay: 0.15s; }
    .leftContainer > *:nth-child(3) { animation-delay: 0.2s; }
    .leftContainer > *:nth-child(4) { animation-delay: 0.25s; }
    .leftContainer > *:nth-child(5) { animation-delay: 0.3s; }
    .leftContainer > *:nth-child(6) { animation-delay: 0.35s; }
    .leftContainer > *:nth-child(7) { animation-delay: 0.4s; }
    .leftContainer > *:nth-child(8) { animation-delay: 0.45s; }
}

@keyframes slideInMenuItem {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* WCAG Compliance - Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .hamMenu,
    .hamMenuLeft,
    .hamMenuCenter,
    .hamMenuRight,
    .menuOverlay,
    .leftContainerWrapper,
    .leftContainer,
    .profileImage {
        transition: none !important;
        animation: none !important;
        transform: none !important;
    }
    
    .leftContainer > * {
        animation: none !important;
        opacity: 1 !important;
        transform: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .hamMenu {
        background: #000000;
        border-bottom: 2px solid #ffffff;
    }
    
    .hamMenuLeft {
        background: #ffffff;
        border: 2px solid #000000;
    }
    
    .hamMenuRight {
        background: #ffffff;
        border: 2px solid #000000;
    }
}

/* Mobile Welcome Message */
.mobileWelcome {
  display: none;
  
  @media screen and (max-width: 576px) {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 16px 20px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.12), rgba(118, 75, 162, 0.12));
    margin-bottom: 8px;
    border-bottom: 1px solid rgba(99, 102, 241, 0.1);
  }
}

.welcomeHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.welcomeText {
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1e293b;
  margin: 0;
}

.mobileStatus {
  display: flex;
  align-items: center;
}

.statusBadge {
  display: inline-flex;
  align-items: center;
  border: none;
  padding: 0;
  background: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.badgeContent {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  @media screen and (max-width: 576px) {
    margin-left: 10px;
  }
}

.badgeIcon {
  width: 14px;
  height: 14px;
}

.badgeText {
  white-space: nowrap;
}

/* Badge Variants */
.badge--success .badgeContent {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.badge--pending .badgeContent {
  background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.badge--warning .badgeContent {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.badge--review .badgeContent {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}
