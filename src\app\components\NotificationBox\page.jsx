"use client";
import React, { useEffect, useRef, useState } from "react";
import styles from "./notificationBox.module.css";
import NotificationItem from "../../components/NotificationList/page";
import { BsBell } from "react-icons/bs";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { usePathname } from "next/navigation";
import { useSSE } from "@/app/context/SSEContext";
import { toast } from "react-toastify";

const page = (props) => {
  const pathname = usePathname();
  const [selectedCard, setSelectedCard] = useState(null);
  const [notificationsArr, setNotificationsArr] = useState([]);
  const [notificationCount, setNotificationCount] = useState(0);

  console.log("notificationsArr", notificationsArr);

  const authTokenRef = useRef();
  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
    if (token) {
      authTokenRef.current = token;
    }
  }

  // Using SSE context for real-time notifications
  const { event } = useSSE();
  console.log("eventSam", event);
  
  // Handle new SSE notifications
  useEffect(() => {
    if (!event || !event.id) {
      return;
    }

    // Add to notifications array if not already present
    setNotificationsArr(prevNotifications => {
      if (prevNotifications.some(n => n.id === event.id)) {
        return prevNotifications;
      }
      return [{
        id: event.id,
        notifiaction_msg: event.notifiaction_msg,
        created_date: event.created_date,
        type: event.type,
        notification_json: event.notification_json,
        user_id: event.user_id
      }, ...prevNotifications];
    });
    
    // Increment notification count only for new notifications
    setNotificationCount(prevCount => prevCount + 1);
  }, [event]);

  // Clear all notifications with API call
  const clearAllNotifications = async () => {
    try {
      await customFetchWithToken.put("/clear-all-notification/");
      setNotificationsArr([]);
      setNotificationCount(0);
      setSelectedCard(null);
    } catch (error) {
      console.error(error);
      toast.error(error.response?.data?.message || "Failed to clear notifications");
    }
  };

  const handleNotificationRemove = async (id) => {
    setSelectedCard(id);
    try {
      await customFetchWithToken.put(`/read-notification/?notification_id=${id}`);
      // Remove specific notification from local state
      setNotificationsArr(prevNotifications => 
        prevNotifications.filter(notification => notification.id !== id)
      );
      // Decrement notification count
      setNotificationCount(prevCount => Math.max(0, prevCount - 1));
    } catch (error) {
      console.error(error);
      toast.error(error.response?.data?.message || "Failed to remove notification");
    }
  };

  // Get all unread notifications on mount
  useEffect(() => {
    const getAllUnreadNotifications = async () => {
      try {
        const response = await customFetchWithToken.get("/get-notification/");
        if (response.status === 200) {
          setNotificationsArr(response.data.data);
          setNotificationCount(response.data.count);
        }
      } catch (error) {
        console.error(error);
        toast.error(error.response?.data?.message || "Failed to fetch notifications");
      }
    };

    const timeoutId = setTimeout(() => {
      getAllUnreadNotifications();
    }, 100);
    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  if (pathname === "/pages/searchads" && !token) {
    return;
  }

  return (
    <div className={styles.rightContainerBell}>
      <button
        type="button"
        className={styles.icon_button}
        onClick={props.handleNotificationDrop}
      >
        <span className={styles.material_icons}>
          <BsBell size={20} />
        </span>
        <span className={styles.icon_button__badge}>{notificationCount}</span>
      </button>
      <div className={styles.notificationContainer}>
        {props.notificationDrop ? (
          <div
            key={Math.floor(Math.random() * 80)}
            className={styles.notificationLists}
          >
            <div className={styles.clearNotificationsCont}>
              {notificationsArr.length > 0 ? (
                <div
                  className={styles.clearNotificationsBtn}
                  onClick={clearAllNotifications}
                >
                  Clear Notifications
                </div>
              ) : (
                <div className={styles.clearNotificationsBtn}>
                  No New Notifications
                </div>
              )}
            </div>
            {notificationsArr.length > 0 ? (
              notificationsArr.map((el, index) => (
                <NotificationItem
                  key={el.id}
                  msg={el?.notifiaction_msg}
                  created_date={el?.created_date}
                  id={el?.id}
                  orderid={el?.notification_json?.order_id}
                  type={el?.type}
                  onRemove={handleNotificationRemove}
                />
              ))
            ) : (
              <div className={styles.noNotifications}>
                You have no new notifications.
              </div>
            )}
          </div>
        ) : (
          ""
        )}
      </div>
    </div>
  );
};

export default page;
