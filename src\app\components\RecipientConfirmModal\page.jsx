"use client";
import { useState, useEffect, useRef } from "react";
import styles from "./reciCon.module.css";
import Modal from "react-modal";
import { getRecipientAccount } from "../../api/searchListing/searchAPI";
import Link from "next/link";
import { createTradeApi } from "../../api/tradeApis/createTrade";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useRouter } from "next/navigation";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";


const page = ({
  payOut_currency,
  modalIsOpen2,
  payOut_option,
  enteredAmount,
  listingId,
  setModalIsOpen2,

}) => {
  const router = useRouter();
  const singleRef = useRef(null);
  const [showModal, setShowModal] = useState(modalIsOpen2);
  const [recipientAcc, setRecipientAcc] = useState([]);
  const [recipientAccType, setRecipientAccType] = useState("");
  const [selectedListingId, setSelectedListingId] = useState(null);
  const [selectedCardId, setSelectedCardId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [orderId, setOrderId] = useState(null);
  


  // Sync showModal with modalIsOpen2 prop
  useEffect(() => {
    setShowModal(modalIsOpen2);
  }, [modalIsOpen2]);



  const handleCardSelect = (id) => {
    setSelectedCardId(id);
    setSelectedListingId(id);
  };


  const handleTradeRequest = async () => {

    console.log("1selectedCardId", listingId, enteredAmount, selectedCardId);
    console.log("1clicked")
    try {
      const payload = {
        listing_id: Number(listingId),
        trade_amount: Number(enteredAmount),
        recipient_id: Number(selectedCardId),
      };
  
      const response = await customFetchWithToken.post("/trade/send-request/", payload);
  
      if (response.status === 200) {
        // toast.success(response.data.message);
        // Toast will be handled by WebSocket response - no duplicate needed
        const { data } = response.data;
        
        const queryParams = new URLSearchParams({
          type: 'user'
        });
        
        setTimeout(() => {
          router.push(`/pages/trade/${data.order_id}?${queryParams.toString()}`);
        }, 1000);
      }
    } catch (error) {
      console.error("error", error);
      toast.error(error.response.data.error);
    }
  };

  const handleCreateTrade = async () => {
    if (selectedCardId == null) {
      toast.error("Select Your Preferred Recipient Account To Trade");
      return;
    }
    try {
      handleTradeRequest();
      // handleSendWebsocketMsg(selectedCardId);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (orderId) {
      router.push(`trade/${orderId}?orderNo=${orderId}`);
    }
  }, [orderId]);

  const handleCloseModal = () => {
    setShowModal(false);
    if (setModalIsOpen2) {
      setModalIsOpen2(false);
    }
  };

  const customStyles = {
    content: {
      top: "50%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      marginRight: "-50%",
      transform: "translate(-50%, -50%)",
      border: "none",
      borderRadius: "16px",
      padding: "0",
      background: "transparent",
      overflow: "visible",
      maxWidth: "90vw",
      maxHeight: "90vh",
    },
    overlay: {
      backgroundColor: "rgba(0, 0, 0, 0.6)",
      backdropFilter: "blur(8px)",
      zIndex: 10000,
    },
  };

  Modal.setAppElement("body");

  const getRecipientAccountTrade = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/get-recipient-account/?payment_option=${payOut_option}`
      );
      setRecipientAcc(res.data.data.data);
      setRecipientAccType(res.data.data.type);
    } catch (error) {
      console.error(error);
    }
  };

  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
  }

  useEffect(() => {
    if (token) {
      setLoading(true);
      getRecipientAccountTrade();
      setLoading(false);
    }
  }, [token]);

  return (
    <div>
      <Modal
        isOpen={showModal}
        contentLabel="Create Trade - Select Recipient Account"
        onRequestClose={handleCloseModal}
        style={customStyles}
        closeTimeoutMS={200}
        aria={{
          labelledby: "modal-title",
          describedby: "modal-description",
        }}
      >
        <div className={styles.modalContainer}>
          {/* Header */}
          <div className={styles.modalHeader}>
            <h2 id="modal-title" className={styles.modalTitle}>
              Create Trade
            </h2>
            <button
              className={styles.closeButton}
              onClick={handleCloseModal}
              aria-label="Close modal"
              type="button"
            >
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className={styles.modalContent}>
            {loading ? (
              <div className={styles.loadingState}>
                <div className={styles.loadingSpinner} aria-label="Loading"></div>
                <p className={styles.loadingText}>Loading recipient accounts...</p>
              </div>
            ) : recipientAcc?.length > 0 ? (
              <div className={styles.recipientSection}>
                <div className={styles.sectionHeader}>
                  <h3 id="modal-description" className={styles.sectionTitle}>
                    Select Recipient Account
                  </h3>
                  <p className={styles.sectionDescription}>
                    Choose from your saved recipient accounts to complete the trade
                  </p>
                </div>

                <div 
                  className={styles.recipientGrid}
                  role="radiogroup"
                  aria-labelledby="modal-description"
                >
                  {recipientAccType === "fiat"
                    ? recipientAcc.map((account, index) => (
                        <div
                          key={account.recipient_id__id}
                          className={`${styles.recipientCard} ${
                            selectedCardId === account.recipient_id__id
                              ? styles.recipientCardSelected
                              : ""
                          }`}
                          onClick={() => handleCardSelect(account.recipient_id__id)}
                          role="radio"
                          aria-checked={selectedCardId === account.recipient_id__id}
                          tabIndex={0}
                          onKeyDown={(e) => {
                            if (e.key === "Enter" || e.key === " ") {
                              e.preventDefault();
                              handleCardSelect(account.recipient_id__id);
                            }
                          }}
                        >
                          <div className={styles.recipientCardContent}>
                            <div className={styles.recipientName}>
                              {account.recipient_id__firstname}
                            </div>
                            <div className={styles.recipientDetail}>
                              <span className={styles.recipientDetailLabel}>
                                {account.data[0].key}:
                              </span>
                              <span className={styles.recipientDetailValue}>
                                {account.data[0].value}
                              </span>
                            </div>
                          </div>
                          <div className={styles.selectionIndicator}>
                            {selectedCardId === account.recipient_id__id && (
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            )}
                          </div>
                        </div>
                      ))
                    : recipientAcc.map((account, index) => (
                        <div
                          key={account.wallet_address}
                          className={`${styles.recipientCard} ${
                            selectedCardId === account.wallet_address
                              ? styles.recipientCardSelected
                              : ""
                          }`}
                          onClick={() => handleCardSelect(account.wallet_address)}
                          role="radio"
                          aria-checked={selectedCardId === account.wallet_address}
                          tabIndex={0}
                          onKeyDown={(e) => {
                            if (e.key === "Enter" || e.key === " ") {
                              e.preventDefault();
                              handleCardSelect(account.wallet_address);
                            }
                          }}
                        >
                          <div className={styles.recipientCardContent}>
                            <div className={styles.recipientName}>
                              Wallet Address
                            </div>
                            <div className={styles.recipientDetail}>
                              <span className={styles.recipientDetailValue}>
                                {account.wallet_address}
                              </span>
                            </div>
                          </div>
                          <div className={styles.selectionIndicator}>
                            {selectedCardId === account.wallet_address && (
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            )}
                          </div>
                        </div>
                      ))}
                </div>
              </div>
            ) : (
              <div className={styles.emptyState}>
                <div className={styles.emptyStateIcon}>
                  <svg
                    width="64"
                    height="64"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                </div>
                <h3 className={styles.emptyStateTitle}>
                  No Recipient Accounts Found
                </h3>
                <p className={styles.emptyStateDescription}>
                  You need to create a recipient account to complete this trade.
                  Add your payment details to get started.
                </p>
                <Link href="/pages/accounts" className={styles.emptyStateAction}>
                  Create Recipient Account
                </Link>
              </div>
            )}
          </div>

          {/* Footer */}
          {recipientAcc?.length > 0 && (
            <div className={styles.modalFooter}>
              <div className={styles.topActions}>
                <button
                  className={styles.secondaryButton}
                  onClick={handleCloseModal}
                  type="button"
                >
                  Cancel
                </button>
                <button
                  className={styles.primaryButton}
                  onClick={handleCreateTrade}
                  disabled={!selectedCardId}
                  type="button"
                >
                  Create Trade
                </button>
              </div>
              <div className={styles.bottomActions}>
                <Link 
                  href="/pages/accounts" 
                  className={`${styles.primaryButton} ${styles.createRecipientBtn}`}
                >
                  Add New Recipient 
                </Link>
              </div>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default page;
