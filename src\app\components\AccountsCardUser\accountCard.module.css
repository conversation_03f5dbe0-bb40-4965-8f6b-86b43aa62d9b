/* Main container for the cards */
.account_container {
    display: flex;
    flex-wrap: wrap;
    gap: 0;
    justify-content: center;
    align-items: flex-start;
    padding: 0;
    background: transparent;
}

/* Modern Card styling with gradients */
.accountCards {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    width: 320px;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    padding: 0;
    font-family: 'Poppins', sans-serif;
    color: #1e293b;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    overflow: hidden;
    position: relative;

    @media screen and (max-width: 1400px) {
        width: 300px;
    }

    @media screen and (max-width: 1200px) {
        width: 280px;
    }

    @media screen and (max-width: 900px) {
        width: 260px;
    }
}

.accountCards::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.accountCards:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-color: #3b82f6;
}

.accountCards:hover::before {
    opacity: 1;
}

/* Card Header */
.cardHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 20px 24px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e2e8f0;
}

.paymentMethodBadge {
    display: flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    padding: 10px 16px;
    border-radius: 24px;
    font-size: 13px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.badgeIcon {
    font-size: 14px;
}

.badgeText {
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.actionButtons {
    display: flex;
    gap: 8px;
}

.actionBtn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.editBtn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.editBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.deleteBtn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.deleteBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* Card Content */
.cardContent {
    padding: 24px;
    min-height: 200px;
}

.paymentDetails {
    display: flex;
    flex-direction: column;
    gap: 18px;
}

.detailItem {
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 16px;
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    border-radius: 10px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.detailItem:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.detailLabel {
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detailValue {
    font-size: 15px;
    font-weight: 500;
    color: #1e293b;
    word-break: break-all;
    line-height: 1.4;
}

.noDetails {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;
    padding: 50px 24px;
    color: #64748b;
    font-size: 15px;
    text-align: center;
}

.noDetailsIcon {
    font-size: 36px;
    opacity: 0.6;
}

/* Card Footer */
.cardFooter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-top: 1px solid #e2e8f0;
}

.statusIndicator {
    display: flex;
    align-items: center;
    gap: 6px;
}

.statusDot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.statusText {
    font-size: 12px;
    font-weight: 500;
    color: #10b981;
}

.cardId {
    font-size: 11px;
    font-weight: 500;
    color: #64748b;
    background: rgba(100, 116, 139, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
}

/* Toast container styles for better integration */
.Toastify__toast-container {
    top: 70px;
    right: 20px;
}

.Toastify__toast {
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}



/* Responsive design for smaller screens */
@media screen and (max-width: 768px) {
    .accountCards {
        width: 100%;
        max-width: 380px;
    }

    .account_container {
        width: 100%;
        justify-content: center;
        padding: 0;
    }

    .cardHeader {
        padding: 18px;
    }

    .paymentMethodBadge {
        font-size: 12px;
        padding: 8px 12px;
    }

    .actionBtn {
        width: 32px;
        height: 32px;
    }

    .cardContent {
        padding: 18px;
        min-height: 180px;
    }

    .detailItem {
        padding: 12px;
    }

    .detailLabel {
        font-size: 11px;
    }

    .detailValue {
        font-size: 14px;
    }

    .cardFooter {
        padding: 14px 18px;
    }
}

/* Mobile specific styles */
@media screen and (max-width: 576px) {
    .accountCards {
        width: 95%;
        max-width: 420px;
        margin: 0 auto;
    }

    .account_container {
        width: 100%;
        justify-content: center;
        align-items: center;
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .cardHeader {
        padding: 20px;
        flex-direction: row;
        gap: 12px;
        align-items: center;
    }

    .paymentMethodBadge {
        font-size: 12px;
        padding: 8px 14px;
    }

    .actionBtn {
        width: 36px;
        height: 36px;
    }

    .cardContent {
        padding: 20px;
        min-height: 160px;
    }

    .detailItem {
        padding: 14px;
    }

    .detailLabel {
        font-size: 11px;
    }

    .detailValue {
        font-size: 14px;
        line-height: 1.4;
    }

    .noDetails {
        padding: 40px 20px;
    }

    .noDetailsIcon {
        font-size: 32px;
    }

    .cardFooter {
        padding: 16px 20px;
        flex-direction: row;
        gap: 12px;
        align-items: center;
        justify-content: space-between;
    }

    .statusIndicator {
        flex: 1;
    }

    .cardId {
        flex-shrink: 0;
    }
}