"use client";
import { useState, useEffect } from "react";
import styles from "./addpay.module.css";
import {
  getUserAddPayList,
  addPaymentUser,
  getAddPayFields,
} from "../../api/userAddPayment/userAddPay";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useRouter } from "next/navigation";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

const page = ({ id, stateFunc, message }) => {
  const router = useRouter();
  const [currencyTo, setCurrencyTo] = useState("");
  const [currencyFrom, setCurrencyFrom] = useState("GBP");
  const [loadCurrencyFrom, setLoadCurrencyFrom] = useState([]);
  const [dataAccepted, setDataAccepted] = useState([]);
  const [messageAccepted, setMessageAccepted] = useState("");
  const [paymentList, setPaymentList] = useState([]);
  const [paymentMethodName, setPaymentMethodName] = useState("");
  const [paymentMethodId, setPaymentMethodId] = useState("");
  const [totalFields, setTotalFields] = useState([]);
  const [accUpiIDs, setAccUpiIDs] = useState([]);

  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

  const handleCurrencyFrom = (event) => {
    setCurrencyFrom(event.target.value);
  };

  const fetchCurrencyDataFrom = async () => {
    try {
      const resCurrency = await fetch(
        `${BaseURL}/get-disable-currency/?currency_from=True&disabled=True`
      );
      const data = await resCurrency.json();
      setLoadCurrencyFrom(data.data);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  const fetchPaymentMethodsFrom = async () => {
    try {
      const resCurrency = await fetch(
        `${BaseURL}/payment-list/?currency=${currencyFrom}`
      );
      // const resCurrency = await fetch(`${BaseURL}/payment-list/?currency=INR`);
      const data = await resCurrency.json();
      setDataAccepted(data.data);
      setMessageAccepted(data.message);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  const handleCurrencyTo = (event) => {
    setCurrencyTo(event.target.value);
  };

  const handlePaymentMethodChange = (event) => {
    setPaymentMethodName(event.target.value);
    const filterPaymentId = paymentList.filter(
      (payment) => payment.payment_method === event.target.value
    );

    setPaymentMethodId(filterPaymentId[0].id);
  };

  const handleEditPayment = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/get-user-payment-fields-data/${id}`
      );

      setPaymentMethodName(res.data.data.payment_method);
      setTotalFields(res.data.data.data);
      setAccUpiIDs(res.data.data.data);
    } catch (error) {
      console.log(error);
    }
  };

  const Data = {
    id: `${id}`,
    data: accUpiIDs,
    payment_method: paymentMethodName,
  };

  const addPayMethodHandler = async () => {
    if (totalFields.length !== accUpiIDs.length) {
      return toast.error("Enter all the input fields to proceed");
    }
    try {
      const res = await customFetchWithToken.put(
        `/edit-user-payment-fields-data/${id}`,
        Data
      );

      if (res.data.status === 200) {
        toast.success(res.data.message);
      }
      setCurrencyFrom("");
      setTotalFields([]);
      setAccUpiIDs([]);
      setTimeout(() => {
        stateFunc(false);
      }, 2000);
    } catch (error) {
      if (error) {
        toast.error("Enter all the input fields");
      }
      console.log("error", error);
    }
  };

  const getPaymentFields = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/user-payment-fields/?payment_method=${paymentMethodName}&currency=${currencyFrom}`
      );

      setTotalFields(res.data.data);
    } catch (error) {
      console.error(error);
    }
  };

  const fetchData = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/payment-list/?currency=${currencyFrom}`
      );

      setPaymentList(res.data.data);
    } catch (e) {
      console.error(e);
    }
  };

  ///
  // this is the issue for revolut👆
  ///
  // useEffect(() => {
  //   fetchData();
  // }, [currencyFrom]);

  useEffect(() => {
    // fetchCurrencyDataTo();
    handleEditPayment();
    // fetchCurrencyDataFrom();
  }, []);

  // useEffect(() => {
  //   fetchPaymentMethodsFrom();
  // }, [currencyFrom]);

  // useEffect(() => {
  //   getPaymentFields();
  // }, [paymentMethodName]);
  return (
    <div className={styles.disputeAction}>
      <div className={styles.disputeActionHearderArea}>
        {/* <div className={styles.disputeActionHearder}>"hello</div> */}
      </div>

      <div className={styles.disputeActionBox}>
        <div className={styles.disputeActionBoxWrapper}>
          {/* <div className={styles.orderDetails}>
            <label htmlFor="order">Select Currency</label>
            <select
              className={styles.payMthodSelect}
              name="currencyPayout"
              id="currencyPayout"
              value={currencyFrom}
              onChange={handleCurrencyFrom}
            >
              <option value="-1">Please select a currency</option>
              {loadCurrencyFrom?.map((currency, index) => (
                <option key={index}>{currency.currency_code}</option>
              ))}
            </select>
          </div> */}
          <div className={styles.orderDetails}>
            <label htmlFor="order">Payment Method Name</label>
            <input
              value={paymentMethodName}
              id="order"
              className={styles.payMthodSelect}
              type="text"
              maxLength={260}
              readOnly
            />
            {/* <select
              className={styles.payMthodSelect}
              name="currencyPayout"
              id="currencyPayout"
              value={paymentMethodName}
            >
              <option value="#">Please select a Payment Method</option>

              {messageAccepted === "Data found."
                ? dataAccepted.map((payMethod, index) => (
                    <option key={index}>{payMethod.payment_method}</option>
                  ))
                : ""}
            </select> */}
          </div>
          {totalFields.map((field, index) => (
            <div className={styles.orderDetails}>
              <label htmlFor="order">{field.key}</label>
              <input
                id="order"
                type="text"
                maxLength={260}
                placeholder={`Enter ${field.key}`}
                onChange={(e) => {
                  const newUpiIds = accUpiIDs.filter(
                    (ids) => ids.key !== field.key
                  );

                  const newUpiIds2 = [
                    ...newUpiIds,
                    { key: field.key, value: e.target.value },
                  ];
                  setAccUpiIDs(newUpiIds2);
                }}
                value={
                  accUpiIDs.find((item) => item.key === field.key)
                    ? accUpiIDs.find((item) => item.key === field.key).value
                    : ""
                }
                required
              />
            </div>
          ))}

          <div
            className={styles.messageSupportBtn}
            onClick={addPayMethodHandler}
          >
            Edit Payment Account
          </div>
        </div>
      </div>
      <ToastContainer />
    </div>
  );
};

export default page;
