/* Import Poppins font */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* Main Container */
.countDownContainer {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 16px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    position: relative;
    overflow: hidden;
    min-width: 200px;
    animation: slideInRight 0.3s ease-out;
}

.countDownContainer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8, #3b82f6);
    border-radius: 12px 12px 0 0;
}

/* Timer Wrapper */
.timerWrapper {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Timer Icon */
.timerIcon {
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 50%;
    color: white;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    animation: pulse 2s infinite;
}

/* Timer Content */
.timerContent {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

/* Timer Label */
.timerLabel {
    font-size: 12px;
    font-weight: 500;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Timer Display */
.timerDisplay {
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Status Indicator */
.statusIndicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(34, 197, 94, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

/* Status Dot */
.statusDot {
    width: 8px;
    height: 8px;
    background: #22c55e;
    border-radius: 50%;
    animation: blink 1.5s infinite;
    box-shadow: 0 0 4px rgba(34, 197, 94, 0.5);
}

/* Status Text */
.statusText {
    font-size: 11px;
    font-weight: 600;
    color: #16a34a;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Animations */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes blink {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.3;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .countDownContainer {
        padding: 12px;
        gap: 10px;
        min-width: 180px;
        border-radius: 10px;
    }

    .timerWrapper {
        gap: 10px;
    }

    .timerIcon {
        width: 32px;
        height: 32px;
        font-size: 18px;
    }

    .timerDisplay {
        font-size: 16px;
    }

    .timerLabel {
        font-size: 11px;
    }

    .statusIndicator {
        padding: 6px 10px;
        border-radius: 6px;
    }

    .statusText {
        font-size: 10px;
    }
}

@media (max-width: 480px) {
    .countDownContainer {
        padding: 10px;
        gap: 8px;
        min-width: 160px;
        border-radius: 8px;
    }

    .timerWrapper {
        gap: 8px;
    }

    .timerIcon {
        width: 28px;
        height: 28px;
        font-size: 16px;
    }

    .timerDisplay {
        font-size: 14px;
    }

    .timerLabel {
        font-size: 10px;
    }

    .statusIndicator {
        padding: 4px 8px;
        border-radius: 4px;
    }

    .statusText {
        font-size: 9px;
    }

    .statusDot {
        width: 6px;
        height: 6px;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .countDownContainer,
    .timerIcon,
    .statusDot {
        animation: none;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .countDownContainer {
        border: 2px solid #000;
        background: #fff;
    }

    .timerDisplay {
        color: #000;
        -webkit-text-fill-color: #000;
    }

    .statusIndicator {
        background: #fff;
        border: 1px solid #000;
    }

    .statusText {
        color: #000;
    }
}
