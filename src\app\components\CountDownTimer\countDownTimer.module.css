/* Simple countdown timer like the existing one */
.countDownTimer {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-size: 18px;
    font-weight: 700;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: #ffffff;
    padding: 12px 20px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.5px;
    min-width: 100px;
    text-align: center;
    animation: pulse 2s infinite;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.countDownTimer::before {
    content: '⏱️ ';
    margin-right: 4px;
    font-size: 11px;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Alert Popup */
.alertPopup {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 24px;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05);
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    margin: 16px 0;
    backdrop-filter: blur(10px);
    animation: slideIn 0.3s ease-out;
    max-width: 400px;
    width: 90%;
}

.alertPopup::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #f59e0b, #ef4444, #f59e0b);
    border-radius: 16px 16px 0 0;
}

.alertPopup p {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 500;
    color: #374151;
    line-height: 1.6;
    text-align: center;
}

.btnContainer {
    display: flex;
    gap: 12px;
    justify-content: center;
    align-items: center;
}

.yesBtn,
.noBtn {
    padding: 12px 24px;
    border: none;
    border-radius: 10px;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
    text-align: center;
}

.yesBtn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.yesBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
}

.noBtn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.noBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

/* Responsive Design */
@media (max-width: 480px) {
    .countDownTimer {
        font-size: 14px;
        padding: 8px 12px;
        border-radius: 6px;
        min-width: 70px;
    }

    .alertPopup {
        padding: 16px;
        margin: 8px 0;
        border-radius: 10px;
        max-width: 320px;
    }

    .alertPopup p {
        font-size: 13px;
        margin-bottom: 14px;
    }

    .btnContainer {
        gap: 8px;
        flex-direction: column;
    }

    .yesBtn,
    .noBtn {
        padding: 12px 20px;
        font-size: 13px;
        width: 100%;
        min-width: unset;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .countDownTimer,
    .alertPopup,
    .yesBtn,
    .noBtn {
        animation: none;
        transition: none;
    }

    .yesBtn:hover,
    .noBtn:hover {
        transform: none;
    }
}
