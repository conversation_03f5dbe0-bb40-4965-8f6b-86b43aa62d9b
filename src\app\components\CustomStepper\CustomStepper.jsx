"use client";
import React from 'react';
import styles from './CustomStepper.module.css';

const CustomStepper = ({
  steps = [],
  activeStep = 0,
  className = '',
  completedColor = '#10B981',
  activeColor = '#3B82F6',
  inactiveColor = '#D1D5DB',
  compact = false
}) => {
  return (
    <div className={`${styles.stepperContainer} ${compact ? styles.compact : ''} ${className}`}>
      {steps.map((step, index) => {
        const isCompleted = index < activeStep;
        const isActive = index === activeStep;
        const isInactive = index > activeStep;

        return (
          <div key={index} className={styles.stepWrapper}>
            {/* Step Circle */}
            <div
              className={`${styles.stepCircle} ${
                isCompleted ? styles.completed :
                isActive ? styles.active :
                styles.inactive
              }`}
              style={{
                backgroundColor: isCompleted ? completedColor :
                               isActive ? activeColor :
                               inactiveColor,
                borderColor: isCompleted ? completedColor :
                           isActive ? activeColor :
                           inactiveColor
              }}
            >
              {isCompleted ? (
                <svg
                  className={styles.checkIcon}
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              ) : (
                <span className={styles.stepNumber}>{index + 1}</span>
              )}
            </div>

            {/* Step Label */}
            <div
              className={`${styles.stepLabel} ${
                isCompleted ? styles.labelCompleted :
                isActive ? styles.labelActive :
                styles.labelInactive
              }`}
            >
              {step.label}
            </div>

            {/* Connector Line */}
            {index < steps.length - 1 && (
              <div
                className={`${styles.connector} ${
                  index < activeStep ? styles.connectorCompleted : styles.connectorInactive
                }`}
                style={{
                  backgroundColor: index < activeStep ? completedColor : inactiveColor
                }}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

export default CustomStepper;
